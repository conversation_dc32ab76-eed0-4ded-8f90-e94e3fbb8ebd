package com.robin.license.server.communication;

import com.robin.license.server.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.util.Iterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 独立的许可证通信服务器
 * 使用NIO实现高性能的许可证验证通信
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@Slf4j
public class LicenseCommunicationServer {
    
    @Value("${license.communication.port:9090}")
    private int communicationPort;
    
    @Value("${license.communication.enabled:true}")
    private boolean communicationEnabled;
    
    @Autowired
    private LicenseService licenseService;
    
    private ServerSocketChannel serverChannel;
    private Selector selector;
    private ExecutorService executorService;
    private volatile boolean running = false;
    
    /**
     * 应用启动完成后自动启动通信服务
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startCommunicationServer() {
        if (!communicationEnabled) {
            log.info("许可证通信服务已禁用");
            return;
        }
        
        try {
            startServer();
        } catch (Exception e) {
            log.error("启动许可证通信服务失败", e);
        }
    }
    
    /**
     * 启动通信服务器
     */
    public void startServer() throws IOException {
        if (running) {
            log.warn("许可证通信服务已在运行");
            return;
        }
        
        // 初始化NIO组件
        selector = Selector.open();
        serverChannel = ServerSocketChannel.open();
        serverChannel.configureBlocking(false);
        serverChannel.bind(new InetSocketAddress(communicationPort));
        serverChannel.register(selector, SelectionKey.OP_ACCEPT);
        
        // 创建线程池处理业务逻辑
        executorService = Executors.newFixedThreadPool(10);
        
        running = true;
        
        log.info("许可证通信服务启动成功，端口: {}", communicationPort);
        
        // 在新线程中运行服务器循环
        Thread serverThread = new Thread(this::serverLoop, "LicenseCommunicationServer");
        serverThread.setDaemon(true);
        serverThread.start();
    }
    
    /**
     * 服务器主循环
     */
    private void serverLoop() {
        while (running) {
            try {
                if (selector.select(1000) > 0) {
                    Iterator<SelectionKey> keyIterator = selector.selectedKeys().iterator();
                    
                    while (keyIterator.hasNext()) {
                        SelectionKey key = keyIterator.next();
                        keyIterator.remove();
                        
                        if (key.isAcceptable()) {
                            handleAccept(key);
                        } else if (key.isReadable()) {
                            handleRead(key);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("许可证通信服务处理请求时出错", e);
            }
        }
    }
    
    /**
     * 处理客户端连接
     */
    private void handleAccept(SelectionKey key) throws IOException {
        ServerSocketChannel serverChannel = (ServerSocketChannel) key.channel();
        SocketChannel clientChannel = serverChannel.accept();
        
        if (clientChannel != null) {
            clientChannel.configureBlocking(false);
            clientChannel.register(selector, SelectionKey.OP_READ);
            log.debug("接受新的客户端连接: {}", clientChannel.getRemoteAddress());
        }
    }
    
    /**
     * 处理客户端数据读取
     */
    private void handleRead(SelectionKey key) {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        
        // 在线程池中处理业务逻辑
        executorService.submit(() -> {
            try {
                processClientRequest(clientChannel);
            } catch (Exception e) {
                log.error("处理客户端请求失败", e);
                try {
                    clientChannel.close();
                } catch (IOException ioException) {
                    log.error("关闭客户端连接失败", ioException);
                }
            }
        });
        
        // 取消读取事件，避免重复处理
        key.cancel();
    }
    
    /**
     * 处理客户端请求
     */
    private void processClientRequest(SocketChannel clientChannel) throws IOException {
        try {
            // 先读取请求长度（4字节）
            ByteBuffer lengthBuffer = ByteBuffer.allocate(4);
            int lengthBytesRead = 0;
            while (lengthBytesRead < 4) {
                int read = clientChannel.read(lengthBuffer);
                if (read < 0) {
                    throw new IOException("连接已关闭");
                }
                lengthBytesRead += read;
            }

            lengthBuffer.flip();
            int requestLength = lengthBuffer.getInt();

            if (requestLength <= 0 || requestLength > 1024 * 1024) { // 限制1MB
                throw new IOException("无效的请求长度: " + requestLength);
            }

            // 读取请求数据
            ByteBuffer requestBuffer = ByteBuffer.allocate(requestLength);
            int requestBytesRead = 0;
            while (requestBytesRead < requestLength) {
                int read = clientChannel.read(requestBuffer);
                if (read < 0) {
                    throw new IOException("连接已关闭");
                }
                requestBytesRead += read;
            }

            requestBuffer.flip();
            byte[] requestData = new byte[requestBuffer.remaining()];
            requestBuffer.get(requestData);

            // 处理请求并生成响应
            byte[] responseData = processLicenseRequest(requestData);

            // 发送响应长度
            ByteBuffer responseLengthBuffer = ByteBuffer.allocate(4);
            responseLengthBuffer.putInt(responseData.length);
            responseLengthBuffer.flip();
            clientChannel.write(responseLengthBuffer);

            // 发送响应数据
            ByteBuffer responseBuffer = ByteBuffer.wrap(responseData);
            clientChannel.write(responseBuffer);

            log.debug("处理许可证请求完成，响应大小: {} 字节", responseData.length);

        } finally {
            // 关闭连接
            clientChannel.close();
        }
    }
    
    /**
     * 处理许可证请求的业务逻辑
     */
    private byte[] processLicenseRequest(byte[] requestData) {
        try {
            String requestJson = new String(requestData, "UTF-8");
            log.debug("收到许可证请求: {}", requestJson);

            // 解析请求类型
            if (requestJson.contains("\"type\":\"verify\"")) {
                return processVerifyRequest(requestJson);
            } else if (requestJson.contains("\"type\":\"heartbeat\"")) {
                return processHeartbeatRequest(requestJson);
            } else {
                return createErrorResponse("未知的请求类型");
            }

        } catch (Exception e) {
            log.error("处理许可证请求失败", e);
            return createErrorResponse("服务器内部错误");
        }
    }

    /**
     * 处理许可证验证请求
     */
    private byte[] processVerifyRequest(String requestJson) {
        try {
            // 解析请求参数
            String licenseId = extractJsonValue(requestJson, "licenseId");
            String fingerprint = extractJsonValue(requestJson, "fingerprint");

            if (licenseId == null || fingerprint == null) {
                return createVerifyResponse(false, "请求参数不完整");
            }

            // 调用许可证服务进行验证
            boolean isValid = licenseService.verifyLicenseByFingerprint(licenseId, fingerprint);

            if (isValid) {
                return createVerifyResponse(true, "许可证验证成功");
            } else {
                return createVerifyResponse(false, "许可证验证失败");
            }

        } catch (Exception e) {
            log.error("处理验证请求失败", e);
            return createVerifyResponse(false, "验证过程出错");
        }
    }

    /**
     * 处理心跳请求
     */
    private byte[] processHeartbeatRequest(String requestJson) {
        try {
            String response = "{\"type\":\"heartbeat\",\"alive\":true,\"timestamp\":" +
                            System.currentTimeMillis() + "}";
            return response.getBytes("UTF-8");
        } catch (Exception e) {
            log.error("处理心跳请求失败", e);
            return createErrorResponse("心跳处理失败");
        }
    }

    /**
     * 创建验证响应
     */
    private byte[] createVerifyResponse(boolean success, String message) {
        try {
            String response = String.format(
                "{\"type\":\"verify\",\"success\":%s,\"message\":\"%s\",\"timestamp\":%d}",
                success, message, System.currentTimeMillis());
            return response.getBytes("UTF-8");
        } catch (Exception e) {
            return createErrorResponse("创建响应失败");
        }
    }

    /**
     * 创建错误响应
     */
    private byte[] createErrorResponse(String message) {
        try {
            String response = String.format(
                "{\"type\":\"error\",\"success\":false,\"message\":\"%s\",\"timestamp\":%d}",
                message, System.currentTimeMillis());
            return response.getBytes("UTF-8");
        } catch (Exception e) {
            return "ERROR".getBytes();
        }
    }

    /**
     * 从JSON字符串中提取值（简单实现）
     */
    private String extractJsonValue(String json, String key) {
        try {
            String searchKey = "\"" + key + "\":\"";
            int start = json.indexOf(searchKey);
            if (start < 0) return null;

            start += searchKey.length();
            int end = json.indexOf("\"", start);
            if (end < 0) return null;

            return json.substring(start, end);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 停止通信服务器
     */
    public void stopServer() {
        running = false;
        
        try {
            if (selector != null) {
                selector.close();
            }
            if (serverChannel != null) {
                serverChannel.close();
            }
            if (executorService != null) {
                executorService.shutdown();
            }
            log.info("许可证通信服务已停止");
        } catch (Exception e) {
            log.error("停止许可证通信服务时出错", e);
        }
    }
    
    /**
     * 获取通信服务状态
     */
    public boolean isRunning() {
        return running;
    }
    
    /**
     * 获取通信端口
     */
    public int getCommunicationPort() {
        return communicationPort;
    }
}
