package com.robin.license.client.network;

import com.robin.license.client.security.ParameterProtector;
import com.robin.license.common.dto.MachineInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * 许可证通信客户端
 * 使用独立的通信端口进行许可证验证
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseCommunicationClient {
    
    private String serverHost;
    private int communicationPort;
    private int timeout;
    
    /**
     * 构造函数，从加密保护中获取配置
     */
    public LicenseCommunicationClient() {
        try {
            // 从加密保护中获取服务器配置
            String serverUrl = ParameterProtector.getServerUrl();
            if (serverUrl != null) {
                URL url = new URL(serverUrl);
                this.serverHost = url.getHost();
            } else {
                this.serverHost = "localhost";
            }
            
            this.communicationPort = ParameterProtector.getCommunicationPort();
            this.timeout = ParameterProtector.getTimeout();
            
            log.debug("通信客户端初始化完成，主机: {}, 端口: {}", serverHost, communicationPort);
            
        } catch (Exception e) {
            log.error("初始化通信客户端失败", e);
            // 使用默认配置
            this.serverHost = "localhost";
            this.communicationPort = 9090;
            this.timeout = 30000;
        }
    }
    
    /**
     * 验证许可证
     * 
     * @param licenseId 许可证ID
     * @param machineInfo 机器信息
     * @return 验证结果
     */
    public boolean verifyLicense(String licenseId, MachineInfo machineInfo) {
        try (Socket socket = new Socket(serverHost, communicationPort)) {
            socket.setSoTimeout(timeout);
            
            // 构建验证请求
            LicenseVerifyRequest request = new LicenseVerifyRequest();
            request.setLicenseId(licenseId);
            request.setMachineFingerprint(machineInfo.generateFingerprint());
            request.setTimestamp(System.currentTimeMillis());
            
            // 发送请求
            sendRequest(socket, request);
            
            // 接收响应
            LicenseVerifyResponse response = receiveResponse(socket);
            
            if (response != null && response.isSuccess()) {
                log.debug("许可证验证成功: {}", licenseId);
                return true;
            } else {
                log.warn("许可证验证失败: {}, 原因: {}", licenseId, 
                        response != null ? response.getMessage() : "无响应");
                return false;
            }
            
        } catch (Exception e) {
            log.error("许可证通信验证失败: " + licenseId, e);
            return false;
        }
    }
    
    /**
     * 心跳检测
     * 
     * @return 服务器是否可达
     */
    public boolean heartbeat() {
        try (Socket socket = new Socket(serverHost, communicationPort)) {
            socket.setSoTimeout(5000); // 心跳超时5秒
            
            // 发送心跳请求
            HeartbeatRequest request = new HeartbeatRequest();
            request.setTimestamp(System.currentTimeMillis());
            
            sendRequest(socket, request);
            
            // 接收响应
            HeartbeatResponse response = receiveResponse(socket);
            
            return response != null && response.isAlive();
            
        } catch (Exception e) {
            log.debug("心跳检测失败", e);
            return false;
        }
    }
    
    /**
     * 发送请求
     */
    private void sendRequest(Socket socket, Object request) throws IOException {
        try (DataOutputStream out = new DataOutputStream(socket.getOutputStream())) {
            String requestJson = serializeRequest(request);
            byte[] requestBytes = requestJson.getBytes(StandardCharsets.UTF_8);
            
            // 发送请求长度
            out.writeInt(requestBytes.length);
            
            // 发送请求数据
            out.write(requestBytes);
            out.flush();
        }
    }
    
    /**
     * 接收响应
     */
    @SuppressWarnings("unchecked")
    private <T> T receiveResponse(Socket socket) throws IOException {
        try (DataInputStream in = new DataInputStream(socket.getInputStream())) {
            // 读取响应长度
            int responseLength = in.readInt();
            if (responseLength <= 0 || responseLength > 1024 * 1024) { // 限制1MB
                throw new IOException("无效的响应长度: " + responseLength);
            }
            
            // 读取响应数据
            byte[] responseBytes = new byte[responseLength];
            in.readFully(responseBytes);
            
            String responseJson = new String(responseBytes, StandardCharsets.UTF_8);
            return (T) deserializeResponse(responseJson);
        }
    }
    
    /**
     * 序列化请求（简单的JSON格式）
     */
    private String serializeRequest(Object request) {
        if (request instanceof LicenseVerifyRequest) {
            LicenseVerifyRequest req = (LicenseVerifyRequest) request;
            return String.format("{\"type\":\"verify\",\"licenseId\":\"%s\",\"fingerprint\":\"%s\",\"timestamp\":%d}",
                    req.getLicenseId(), req.getMachineFingerprint(), req.getTimestamp());
        } else if (request instanceof HeartbeatRequest) {
            HeartbeatRequest req = (HeartbeatRequest) request;
            return String.format("{\"type\":\"heartbeat\",\"timestamp\":%d}", req.getTimestamp());
        }
        return "{}";
    }
    
    /**
     * 反序列化响应（简单的JSON解析）
     */
    private Object deserializeResponse(String json) {
        try {
            if (json.contains("\"type\":\"verify\"")) {
                LicenseVerifyResponse response = new LicenseVerifyResponse();
                response.setSuccess(json.contains("\"success\":true"));
                
                // 提取消息
                int msgStart = json.indexOf("\"message\":\"");
                if (msgStart > 0) {
                    msgStart += 11;
                    int msgEnd = json.indexOf("\"", msgStart);
                    if (msgEnd > msgStart) {
                        response.setMessage(json.substring(msgStart, msgEnd));
                    }
                }
                
                return response;
            } else if (json.contains("\"type\":\"heartbeat\"")) {
                HeartbeatResponse response = new HeartbeatResponse();
                response.setAlive(json.contains("\"alive\":true"));
                return response;
            }
        } catch (Exception e) {
            log.error("解析响应失败", e);
        }
        
        return null;
    }
    
    // 内部请求响应类
    private static class LicenseVerifyRequest {
        private String licenseId;
        private String machineFingerprint;
        private long timestamp;
        
        public String getLicenseId() { return licenseId; }
        public void setLicenseId(String licenseId) { this.licenseId = licenseId; }
        public String getMachineFingerprint() { return machineFingerprint; }
        public void setMachineFingerprint(String machineFingerprint) { this.machineFingerprint = machineFingerprint; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
    
    private static class LicenseVerifyResponse {
        private boolean success;
        private String message;
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
    
    private static class HeartbeatRequest {
        private long timestamp;
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
    
    private static class HeartbeatResponse {
        private boolean alive;
        
        public boolean isAlive() { return alive; }
        public void setAlive(boolean alive) { this.alive = alive; }
    }
}
