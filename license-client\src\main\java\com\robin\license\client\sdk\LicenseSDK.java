package com.robin.license.client.sdk;

import com.robin.license.client.core.LicenseValidator;
import com.robin.license.client.hardware.MachineInfoCollector;
import com.robin.license.client.network.LicenseClient;
import com.robin.license.client.network.LicenseCommunicationClient;
import com.robin.license.client.security.ParameterProtector;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 许可证SDK自动化入口类
 * 提供自动初始化、机器注册、定时验证等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseSDK {
    
    private static final LicenseSDK INSTANCE = new LicenseSDK();
    
    private LicenseClient licenseClient;
    private LicenseCommunicationClient communicationClient;
    private ScheduledExecutorService scheduler;
    private String serverUrl;
    private AtomicBoolean initialized = new AtomicBoolean(false);
    private AtomicBoolean licenseValid = new AtomicBoolean(false);
    private boolean strictMode = true; // 严格模式：无证书直接退出
    private LicenseInfo cachedLicenseInfo;
    
    private LicenseSDK() {
        // 私有构造函数
    }
    
    /**
     * 获取SDK实例
     * 
     * @return SDK实例
     */
    public static LicenseSDK getInstance() {
        return INSTANCE;
    }
    
    /**
     * 自动初始化SDK
     * 使用加密保护的参数，无需传入明文配置
     */
    public void autoInitialize() {
        autoInitialize(true);
    }

    /**
     * 自动初始化SDK
     * 使用加密保护的参数，无需传入明文配置
     *
     * @param strictMode 是否启用严格模式（无证书直接退出）
     */
    public void autoInitialize(boolean strictMode) {
        if (initialized.get()) {
            log.warn("SDK已经初始化，跳过重复初始化");
            return;
        }

        this.strictMode = strictMode;

        try {
            // 1. 从加密保护中获取服务器配置
            String serverUrl = ParameterProtector.getServerUrl();
            if (serverUrl == null) {
                handleLicenseFailure("无法获取服务器配置，请检查SDK配置");
                return;
            }

            this.serverUrl = serverUrl;
            log.info("开始自动初始化许可证SDK，严格模式: {}", strictMode);

            // 2. 初始化客户端
            licenseClient = new LicenseClient(serverUrl);
            communicationClient = new LicenseCommunicationClient();
            
            // 3. 获取机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();

            log.info("机器信息收集完成，机器ID: {}", machineInfo.getMachineId());

            // 4. 尝试本地验证
            boolean hasLocalLicense = tryLocalValidation();

            // 5. 如果没有本地证书，尝试向服务端同步
            if (!hasLocalLicense) {
                log.info("本地未找到有效证书，尝试向服务端同步许可证");
                boolean synced = syncLicense(machineInfo);

                if (!synced) {
                    handleLicenseFailure("许可证同步失败，无法获取许可证");
                    return;
                }

                // 同步成功后再次尝试本地验证
                hasLocalLicense = tryLocalValidation();
            }
            
            if (!hasLocalLicense) {
                handleLicenseFailure("许可证验证失败，无法启动应用");
                return;
            }
            
            // 6. 启动定时验证任务
            startPeriodicValidation();

            // 7. 标记初始化完成
            initialized.set(true);
            licenseValid.set(true);
            
            log.info("许可证SDK初始化成功");
            
        } catch (Exception e) {
            log.error("许可证SDK初始化失败", e);
            handleLicenseFailure("SDK初始化异常: " + e.getMessage());
        }
    }
    
    /**
     * 尝试本地验证
     *
     * @return 是否验证成功
     */
    private boolean tryLocalValidation() {
        try {
            LicenseInfo licenseInfo = LicenseValidator.validateLicense();
            this.cachedLicenseInfo = licenseInfo;
            log.info("本地许可证验证成功，客户: {}, 过期时间: {}",
                    licenseInfo.getCustomerName(), licenseInfo.getExpireTime());
            return true;
        } catch (LicenseException e) {
            log.warn("本地许可证验证失败: {}", e.getMessage());
            this.cachedLicenseInfo = null;
            return false;
        }
    }
    
    /**
     * 向服务端同步许可证
     *
     * @param machineInfo 机器信息
     * @return 是否同步成功
     */
    private boolean syncLicense(MachineInfo machineInfo) {
        try {
            boolean synced = licenseClient.syncLicense(machineInfo);
            if (synced) {
                log.info("许可证同步成功，文件已更新到本地");
            }
            return synced;
        } catch (Exception e) {
            log.error("许可证同步失败", e);
            return false;
        }
    }
    
    /**
     * 启动定时验证任务
     */
    private void startPeriodicValidation() {
        scheduler = Executors.newScheduledThreadPool(2);
        
        // 每5分钟进行本地验证
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 进行本地验证
                this.cachedLicenseInfo = LicenseValidator.validateLicense();
                licenseValid.set(true);
                log.debug("定时本地验证成功");

            } catch (Exception e) {
                log.error("定时本地验证失败", e);
                cachedLicenseInfo = null;
                licenseValid.set(false);
                if (strictMode) {
                    handleLicenseFailure("本地验证失败，应用即将退出");
                }
            }
        }, 5, 5, TimeUnit.MINUTES);
        
        // 每30分钟进行通信端口验证
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (cachedLicenseInfo != null && communicationClient != null) {
                    // 使用通信端口进行快速验证
                    MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
                    boolean isValid = communicationClient.verifyLicense(
                            cachedLicenseInfo.getLicenseId(), machineInfo);

                    if (isValid) {
                        log.debug("通信端口验证成功");
                        licenseValid.set(true);
                    } else {
                        log.warn("通信端口验证失败，尝试完整同步");
                        // 通信验证失败，尝试完整同步
                        boolean synced = syncLicense(machineInfo);
                        if (synced) {
                            LicenseInfo licenseInfo = LicenseValidator.validateLicense();
                            this.cachedLicenseInfo = licenseInfo;
                            log.info("完整同步验证成功");
                            licenseValid.set(true);
                        } else {
                            log.error("完整同步也失败");
                            licenseValid.set(false);
                            if (strictMode) {
                                handleLicenseFailure("许可证验证失败，应用即将退出");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("定时通信验证失败: {}", e.getMessage());
                // 同步失败不直接退出，但记录日志
            }
        }, 30, 30, TimeUnit.MINUTES);

        // 每2小时进行心跳检测
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (communicationClient != null) {
                    boolean alive = communicationClient.heartbeat();
                    if (!alive) {
                        log.warn("服务器心跳检测失败");
                    } else {
                        log.debug("服务器心跳检测正常");
                    }
                }
            } catch (Exception e) {
                log.debug("心跳检测异常: {}", e.getMessage());
            }
        }, 2, 2, TimeUnit.HOURS);
        
        log.info("定时验证任务启动成功");
    }
    
    /**
     * 处理许可证失败情况
     * 
     * @param message 失败消息
     */
    private void handleLicenseFailure(String message) {
        log.error(message);
        licenseValid.set(false);
        
        if (strictMode) {
            log.error("严格模式下许可证验证失败，应用即将退出");
            System.err.println("许可证验证失败: " + message);
            System.err.println("应用无法继续运行，即将退出...");
            
            // 延迟3秒后退出，给用户看到错误信息的时间
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            System.exit(1);
        }
    }
    
    /**
     * 检查许可证是否有效
     * 
     * @return 是否有效
     */
    public boolean isLicenseValid() {
        return initialized.get() && licenseValid.get();
    }
    
    /**
     * 获取许可证信息
     *
     * @return 许可证信息
     */
    public LicenseInfo getLicenseInfo() {
        return cachedLicenseInfo;
    }
    
    /**
     * 关闭SDK
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        

        
        if (licenseClient != null) {
            licenseClient.close();
        }
        
        initialized.set(false);
        licenseValid.set(false);
        
        log.info("许可证SDK已关闭");
    }

    /**
     * 掩码URL，保护敏感信息不在日志中暴露
     */
    private String maskUrl(String url) {
        if (url == null || url.length() < 10) {
            return "***";
        }

        // 只显示协议和部分域名
        try {
            java.net.URL urlObj = new java.net.URL(url);
            String host = urlObj.getHost();
            if (host != null && host.length() > 4) {
                String maskedHost = host.substring(0, 2) + "***" + host.substring(host.length() - 2);
                return urlObj.getProtocol() + "://" + maskedHost + ":" + urlObj.getPort();
            }
        } catch (Exception e) {
            // 解析失败，返回通用掩码
        }

        return url.substring(0, Math.min(8, url.length())) + "***";
    }
}
