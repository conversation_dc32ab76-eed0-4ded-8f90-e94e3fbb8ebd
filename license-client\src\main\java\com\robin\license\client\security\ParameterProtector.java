package com.robin.license.client.security;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.io.InputStream;

/**
 * SDK参数保护器
 * 对敏感参数进行加密保护，防止反编译和反射调用
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public final class ParameterProtector {
    
    // 混淆的常量，防止直接字符串搜索
    private static final byte[] SALT_BYTES = {
        0x4C, 0x69, 0x63, 0x65, 0x6E, 0x73, 0x65, 0x53,
        0x44, 0x4B, 0x50, 0x72, 0x6F, 0x74, 0x65, 0x63
    };
    
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "AES";
    
    // 预加密的参数映射表
    private static final Map<String, String> ENCRYPTED_PARAMS = new HashMap<>();
    
    static {
        // 在类加载时初始化加密参数
        initializeEncryptedParams();
        // 尝试从配置文件加载参数
        loadFromConfigFile();
    }
    
    /**
     * 初始化加密参数
     * 这些参数在编译时就被加密，运行时解密使用
     */
    private static void initializeEncryptedParams() {
        try {
            // 默认参数（作为后备）
            ENCRYPTED_PARAMS.put("SERVER_URL", encryptParam("http://localhost:8080"));
            ENCRYPTED_PARAMS.put("COMM_PORT", encryptParam("9090"));
            ENCRYPTED_PARAMS.put("TIMEOUT", encryptParam("30000"));
            ENCRYPTED_PARAMS.put("RETRY_COUNT", encryptParam("3"));

        } catch (Exception e) {
            // 静默处理，不暴露加密失败信息
            throw new RuntimeException("Parameter initialization failed", e);
        }
    }

    /**
     * 从配置文件加载加密参数
     */
    private static void loadFromConfigFile() {
        try {
            // 尝试从类路径加载配置文件
            InputStream is = ParameterProtector.class.getClassLoader()
                    .getResourceAsStream("sdk_config.properties");

            if (is != null) {
                Properties props = new Properties();
                props.load(is);

                // 覆盖默认参数
                for (String key : props.stringPropertyNames()) {
                    if (key.startsWith("SERVER_URL") || key.startsWith("COMM_PORT") ||
                        key.startsWith("TIMEOUT") || key.startsWith("RETRY_COUNT")) {
                        ENCRYPTED_PARAMS.put(key, props.getProperty(key));
                    }
                }

                is.close();
            }
        } catch (Exception e) {
            // 静默处理，使用默认参数
        }
    }
    
    /**
     * 获取解密后的参数值
     * 
     * @param paramKey 参数键
     * @return 解密后的参数值
     */
    public static String getProtectedParam(String paramKey) {
        try {
            String encryptedValue = ENCRYPTED_PARAMS.get(paramKey);
            if (encryptedValue == null) {
                return null;
            }
            return decryptParam(encryptedValue);
        } catch (Exception e) {
            // 返回null而不是抛出异常，增加安全性
            return null;
        }
    }
    
    /**
     * 获取服务器URL
     */
    public static String getServerUrl() {
        return getProtectedParam("SERVER_URL");
    }
    
    /**
     * 获取通信端口
     */
    public static int getCommunicationPort() {
        String port = getProtectedParam("COMM_PORT");
        try {
            return port != null ? Integer.parseInt(port) : 9090;
        } catch (NumberFormatException e) {
            return 9090;
        }
    }
    
    /**
     * 获取超时时间
     */
    public static int getTimeout() {
        String timeout = getProtectedParam("TIMEOUT");
        try {
            return timeout != null ? Integer.parseInt(timeout) : 30000;
        } catch (NumberFormatException e) {
            return 30000;
        }
    }
    
    /**
     * 获取重试次数
     */
    public static int getRetryCount() {
        String retryCount = getProtectedParam("RETRY_COUNT");
        try {
            return retryCount != null ? Integer.parseInt(retryCount) : 3;
        } catch (NumberFormatException e) {
            return 3;
        }
    }
    
    /**
     * 加密参数
     */
    private static String encryptParam(String plainText) throws Exception {
        SecretKeySpec keySpec = generateKey();
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        
        // 生成随机IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        
        // 将IV和加密数据组合
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);
        
        return Base64.getEncoder().encodeToString(combined);
    }
    
    /**
     * 解密参数
     */
    private static String decryptParam(String encryptedText) throws Exception {
        byte[] combined = Base64.getDecoder().decode(encryptedText);
        
        // 分离IV和加密数据
        byte[] iv = new byte[16];
        byte[] encrypted = new byte[combined.length - 16];
        System.arraycopy(combined, 0, iv, 0, 16);
        System.arraycopy(combined, 16, encrypted, 0, encrypted.length);
        
        SecretKeySpec keySpec = generateKey();
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(encrypted);
        
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    /**
     * 生成加密密钥
     * 使用多种混淆技术生成密钥
     */
    private static SecretKeySpec generateKey() throws Exception {
        // 使用多层混淆生成密钥
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        
        // 混合多种信息生成密钥
        digest.update(SALT_BYTES);
        digest.update(getClassHash());
        digest.update(getSystemHash());
        
        byte[] keyBytes = digest.digest();
        
        // 只使用前16字节作为AES密钥
        byte[] aesKey = new byte[16];
        System.arraycopy(keyBytes, 0, aesKey, 0, 16);
        
        return new SecretKeySpec(aesKey, KEY_ALGORITHM);
    }
    
    /**
     * 获取类的哈希值（防止类被修改）
     */
    private static byte[] getClassHash() {
        try {
            String className = ParameterProtector.class.getName();
            return className.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            return new byte[]{0x01, 0x02, 0x03, 0x04};
        }
    }
    
    /**
     * 获取系统相关的哈希值
     */
    private static byte[] getSystemHash() {
        try {
            String javaVersion = System.getProperty("java.version", "unknown");
            return javaVersion.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            return new byte[]{0x05, 0x06, 0x07, 0x08};
        }
    }
    
    /**
     * 防止实例化
     */
    private ParameterProtector() {
        throw new UnsupportedOperationException("This class cannot be instantiated");
    }
    
    /**
     * 防止反射调用
     */
    static {
        // 检查调用栈，防止反射调用
        StackTraceElement[] stack = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stack) {
            String className = element.getClassName();
            if (className.contains("reflect") || className.contains("Reflection")) {
                throw new SecurityException("Reflection access denied");
            }
        }
    }
}
