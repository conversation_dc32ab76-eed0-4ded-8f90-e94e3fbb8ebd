package com.robin.license.client.security;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Properties;

/**
 * 配置文件生成器
 * 生成加密的配置文件，替代明文配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ConfigGenerator {
    
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "AES";
    
    // 与ParameterProtector相同的盐值
    private static final byte[] SALT_BYTES = {
        0x4C, 0x69, 0x63, 0x65, 0x6E, 0x73, 0x65, 0x53,
        0x44, 0x4B, 0x50, 0x72, 0x6F, 0x74, 0x65, 0x63
    };
    
    /**
     * 生成加密配置文件
     * 
     * @param configFilePath 配置文件路径
     * @param serverUrl 服务器URL
     * @param commPort 通信端口
     * @param timeout 超时时间
     * @param retryCount 重试次数
     */
    public static void generateEncryptedConfig(String configFilePath, 
                                             String serverUrl, 
                                             String commPort, 
                                             String timeout, 
                                             String retryCount) throws Exception {
        
        Properties props = new Properties();
        
        // 加密各个参数
        props.setProperty("SERVER_URL", encryptValue(serverUrl));
        props.setProperty("COMM_PORT", encryptValue(commPort));
        props.setProperty("TIMEOUT", encryptValue(timeout));
        props.setProperty("RETRY_COUNT", encryptValue(retryCount));
        
        // 添加校验信息
        props.setProperty("CONFIG_VERSION", "1.0");
        props.setProperty("GENERATED_TIME", String.valueOf(System.currentTimeMillis()));
        
        // 保存到文件
        try (FileWriter writer = new FileWriter(configFilePath)) {
            props.store(writer, "Encrypted License SDK Configuration");
        }
        
        System.out.println("加密配置文件已生成: " + configFilePath);
    }
    
    /**
     * 加密单个值
     */
    private static String encryptValue(String plainText) throws Exception {
        SecretKeySpec keySpec = generateKey();
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        
        // 生成随机IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        
        // 将IV和加密数据组合
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);
        
        return Base64.getEncoder().encodeToString(combined);
    }
    
    /**
     * 生成与ParameterProtector相同的密钥
     */
    private static SecretKeySpec generateKey() throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        
        digest.update(SALT_BYTES);
        digest.update(getClassHash());
        digest.update(getSystemHash());
        
        byte[] keyBytes = digest.digest();
        
        byte[] aesKey = new byte[16];
        System.arraycopy(keyBytes, 0, aesKey, 0, 16);
        
        return new SecretKeySpec(aesKey, KEY_ALGORITHM);
    }
    
    private static byte[] getClassHash() {
        try {
            // 使用ParameterProtector的类名保持一致
            String className = "com.robin.license.client.security.ParameterProtector";
            return className.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            return new byte[]{0x01, 0x02, 0x03, 0x04};
        }
    }
    
    private static byte[] getSystemHash() {
        try {
            String javaVersion = System.getProperty("java.version", "unknown");
            return javaVersion.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            return new byte[]{0x05, 0x06, 0x07, 0x08};
        }
    }
    
    /**
     * 主方法，用于生成配置文件
     */
    public static void main(String[] args) {
        try {
            // 示例配置
            String configPath = "sdk_config.properties";
            String serverUrl = "http://localhost:8080";
            String commPort = "9090";
            String timeout = "30000";
            String retryCount = "3";
            
            // 如果有命令行参数，使用命令行参数
            if (args.length >= 5) {
                configPath = args[0];
                serverUrl = args[1];
                commPort = args[2];
                timeout = args[3];
                retryCount = args[4];
            }
            
            generateEncryptedConfig(configPath, serverUrl, commPort, timeout, retryCount);
            
            System.out.println("配置文件生成完成！");
            System.out.println("使用方法：");
            System.out.println("String serverUrl = ParameterProtector.getServerUrl();");
            System.out.println("int commPort = ParameterProtector.getCommunicationPort();");
            
        } catch (Exception e) {
            System.err.println("生成配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
