# 这是一个示例配置文件
# 实际使用时，请使用 ConfigGenerator 生成加密的配置文件

# 服务器配置示例（这些值会被加密）
# SERVER_URL=http://localhost:8080
# COMM_PORT=9090
# TIMEOUT=30000
# RETRY_COUNT=3

# 使用方法：
# 1. 运行 ConfigGenerator.main() 生成加密配置文件
# 2. 将生成的 sdk_config.properties 放到 classpath 根目录
# 3. SDK 会自动加载加密配置

# 生成命令示例：
# java -cp license-client.jar com.robin.license.client.security.ConfigGenerator \
#   sdk_config.properties \
#   http://your-server:8080 \
#   9090 \
#   30000 \
#   3

# 配置说明：
# SERVER_URL: 许可证服务器地址
# COMM_PORT: 通信端口（独立于HTTP端口）
# TIMEOUT: 网络超时时间（毫秒）
# RETRY_COUNT: 重试次数
